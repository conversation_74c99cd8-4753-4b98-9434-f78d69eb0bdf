module.exports = {

"[next]/internal/font/google/inter_1091e993.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "inter_1091e993-module__SC2QLa__className",
  "variable": "inter_1091e993-module__SC2QLa__variable",
});
}}),
"[next]/internal/font/google/inter_1091e993.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_1091e993.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'Inter', 'Inter Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[next]/internal/font/google/jetbrains_mono_db724454.module.css [app-rsc] (css module)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v({
  "className": "jetbrains_mono_db724454-module__BnDF7a__className",
  "variable": "jetbrains_mono_db724454-module__BnDF7a__variable",
});
}}),
"[next]/internal/font/google/jetbrains_mono_db724454.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__ = __turbopack_context__.i("[next]/internal/font/google/jetbrains_mono_db724454.module.css [app-rsc] (css module)");
;
const fontData = {
    className: __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].className,
    style: {
        fontFamily: "'JetBrains Mono', 'JetBrains Mono Fallback'",
        fontStyle: "normal"
    }
};
if (__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable != null) {
    fontData.variable = __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$module$2e$css__$5b$app$2d$rsc$5d$__$28$css__module$29$__["default"].variable;
}
const __TURBOPACK__default__export__ = fontData;
}}),
"[project]/src/lib/constants.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// App Configuration
__turbopack_context__.s({
    "ANIMATION_DURATION": (()=>ANIMATION_DURATION),
    "ANIMATION_PRESETS": (()=>ANIMATION_PRESETS),
    "API_CONFIG": (()=>API_CONFIG),
    "APP_CONFIG": (()=>APP_CONFIG),
    "BREAKPOINTS": (()=>BREAKPOINTS),
    "COLORS": (()=>COLORS),
    "DEMO_CONFIG": (()=>DEMO_CONFIG),
    "ENTITY_TYPES": (()=>ENTITY_TYPES),
    "FAQ_DATA": (()=>FAQ_DATA),
    "FEATURES": (()=>FEATURES),
    "NAVIGATION": (()=>NAVIGATION),
    "POST_TAGS": (()=>POST_TAGS),
    "PRICING_PLANS": (()=>PRICING_PLANS),
    "REPUTATION_LEVELS": (()=>REPUTATION_LEVELS),
    "SOCIAL_LINKS": (()=>SOCIAL_LINKS),
    "TESTIMONIALS": (()=>TESTIMONIALS)
});
const APP_CONFIG = {
    name: 'MessageYou',
    tagline: 'Snap. Post. Expose. Remember.',
    description: 'Visual-first platform for civic reporting and community transparency',
    version: '1.0.0',
    author: 'MessageYou Team'
};
const API_CONFIG = {
    baseUrl: '/api',
    timeout: 5000,
    retries: 3
};
const ENTITY_TYPES = {
    PERSON: 'person',
    BUSINESS: 'business',
    PLACE: 'place',
    VEHICLE: 'vehicle',
    ITEM: 'item',
    EVENT: 'event'
};
const POST_TAGS = {
    REVIEW: 'review',
    SCAM: 'scam',
    LOST: 'lost',
    FOUND: 'found',
    GOOD_DEED: 'good_deed',
    WARNING: 'warning',
    QUESTION: 'question',
    UPDATE: 'update'
};
const REPUTATION_LEVELS = {
    EXCELLENT: {
        min: 4.5,
        label: 'Excellent',
        color: 'green'
    },
    GOOD: {
        min: 3.5,
        label: 'Good',
        color: 'blue'
    },
    AVERAGE: {
        min: 2.5,
        label: 'Average',
        color: 'yellow'
    },
    POOR: {
        min: 1.5,
        label: 'Poor',
        color: 'orange'
    },
    TERRIBLE: {
        min: 0,
        label: 'Terrible',
        color: 'red'
    }
};
const ANIMATION_DURATION = {
    FAST: 0.3,
    NORMAL: 0.5,
    SLOW: 0.8,
    VERY_SLOW: 1.2
};
const BREAKPOINTS = {
    SM: 640,
    MD: 768,
    LG: 1024,
    XL: 1280,
    '2XL': 1536
};
const COLORS = {
    primary: {
        50: '#f0f9ff',
        100: '#e0f2fe',
        200: '#bae6fd',
        300: '#7dd3fc',
        400: '#38bdf8',
        500: '#0ea5e9',
        600: '#0284c7',
        700: '#0369a1',
        800: '#075985',
        900: '#0c4a6e'
    },
    accent: {
        50: '#fdf4ff',
        100: '#fae8ff',
        200: '#f5d0fe',
        300: '#f0abfc',
        400: '#e879f9',
        500: '#d946ef',
        600: '#c026d3',
        700: '#a21caf',
        800: '#86198f',
        900: '#701a75'
    },
    success: {
        50: '#f0fdf4',
        100: '#dcfce7',
        200: '#bbf7d0',
        300: '#86efac',
        400: '#4ade80',
        500: '#22c55e',
        600: '#16a34a',
        700: '#15803d',
        800: '#166534',
        900: '#14532d'
    },
    warning: {
        50: '#fffbeb',
        100: '#fef3c7',
        200: '#fde68a',
        300: '#fcd34d',
        400: '#fbbf24',
        500: '#f59e0b',
        600: '#d97706',
        700: '#b45309',
        800: '#92400e',
        900: '#78350f'
    },
    danger: {
        50: '#fef2f2',
        100: '#fee2e2',
        200: '#fecaca',
        300: '#fca5a5',
        400: '#f87171',
        500: '#ef4444',
        600: '#dc2626',
        700: '#b91c1c',
        800: '#991b1b',
        900: '#7f1d1d'
    }
};
const DEMO_CONFIG = {
    maxEntities: 50,
    maxPostsPerEntity: 20,
    maxCommentsPerPost: 10,
    simulationDelay: 2000,
    typingSpeed: 50
};
const FEATURES = {
    PHOTO_UPLOAD: true,
    AI_ANALYSIS: true,
    REAL_TIME_UPDATES: true,
    NOTIFICATIONS: true,
    DARK_MODE: true,
    ANALYTICS: false,
    BETA_FEATURES: true
};
const NAVIGATION = [
    {
        name: 'Home',
        href: '/',
        icon: 'Home'
    },
    {
        name: 'Demo',
        href: '/demo',
        icon: 'Play'
    },
    {
        name: 'Pitch',
        href: '/pitch',
        icon: 'Presentation'
    },
    {
        name: 'Why Us',
        href: '/why-us',
        icon: 'Users'
    },
    {
        name: 'Roadmap',
        href: '/roadmap',
        icon: 'Map'
    },
    {
        name: 'Sign Up',
        href: '/signup',
        icon: 'UserPlus'
    }
];
const SOCIAL_LINKS = [
    {
        name: 'Twitter',
        href: '#',
        icon: 'Twitter'
    },
    {
        name: 'LinkedIn',
        href: '#',
        icon: 'Linkedin'
    },
    {
        name: 'GitHub',
        href: '#',
        icon: 'Github'
    },
    {
        name: 'Discord',
        href: '#',
        icon: 'MessageCircle'
    }
];
const PRICING_PLANS = [
    {
        name: 'Free',
        price: 0,
        period: 'forever',
        description: 'Perfect for getting started',
        features: [
            'Basic photo upload',
            'View public posts',
            'Basic search',
            'Limited posts (10/month)',
            'Community support'
        ],
        cta: 'Get Started',
        popular: false
    },
    {
        name: 'Premium',
        price: 4.99,
        period: 'month',
        description: 'For active community members',
        features: [
            'Unlimited posts',
            'Advanced AI analysis',
            'Priority search results',
            'Trend alerts',
            'Enhanced privacy',
            'Email support'
        ],
        cta: 'Start Free Trial',
        popular: true
    },
    {
        name: 'Business',
        price: 19.99,
        period: 'month',
        description: 'For businesses and organizations',
        features: [
            'Entity claiming',
            'Business dashboard',
            'Response tools',
            'Analytics & insights',
            'API access',
            'Priority support'
        ],
        cta: 'Contact Sales',
        popular: false
    }
];
const TESTIMONIALS = [
    {
        name: 'Sarah Chen',
        role: 'Community Organizer',
        avatar: '/images/avatars/sarah.jpg',
        content: 'MessageYou helped our neighborhood identify and stop a series of scams. The visual-first approach makes reporting so much easier.',
        rating: 5
    },
    {
        name: 'Mike Rodriguez',
        role: 'Small Business Owner',
        avatar: '/images/avatars/mike.jpg',
        content: 'Being able to respond to community feedback directly has improved our customer relationships tremendously.',
        rating: 5
    },
    {
        name: 'Dr. Emily Watson',
        role: 'Urban Planning Researcher',
        avatar: '/images/avatars/emily.jpg',
        content: 'This platform provides invaluable data for understanding community dynamics and safety patterns.',
        rating: 5
    }
];
const FAQ_DATA = [
    {
        question: 'How does the photo recognition work?',
        answer: 'Our AI analyzes uploaded photos to identify people, places, businesses, and objects, then matches them to existing entities in our database or creates new ones.'
    },
    {
        question: 'Is my personal information safe?',
        answer: 'We prioritize privacy with optional anonymous posting, data encryption, and strict compliance with privacy regulations like GDPR and CCPA.'
    },
    {
        question: 'Can businesses respond to posts about them?',
        answer: 'Yes, verified businesses can claim their entities and respond to posts, providing transparency and accountability for all parties.'
    },
    {
        question: 'How do you prevent false reports?',
        answer: 'We use AI-powered content moderation, community voting, and human review to identify and filter out false or malicious content.'
    },
    {
        question: 'What types of entities can be reported?',
        answer: 'You can report on people, businesses, places, vehicles, lost items, events, and more. If it exists in the real world, it can be documented on MessageYou.'
    }
];
const ANIMATION_PRESETS = {
    fadeIn: {
        initial: {
            opacity: 0
        },
        animate: {
            opacity: 1
        },
        transition: {
            duration: 0.5
        }
    },
    slideUp: {
        initial: {
            opacity: 0,
            y: 50
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6
        }
    },
    slideDown: {
        initial: {
            opacity: 0,
            y: -50
        },
        animate: {
            opacity: 1,
            y: 0
        },
        transition: {
            duration: 0.6
        }
    },
    scaleIn: {
        initial: {
            opacity: 0,
            scale: 0.8
        },
        animate: {
            opacity: 1,
            scale: 1
        },
        transition: {
            duration: 0.5
        }
    },
    rotateIn: {
        initial: {
            opacity: 0,
            rotate: -10
        },
        animate: {
            opacity: 1,
            rotate: 0
        },
        transition: {
            duration: 0.7
        }
    }
};
}}),
"[project]/src/app/layout.js [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>RootLayout),
    "metadata": (()=>metadata),
    "viewport": (()=>viewport)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/inter_1091e993.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[next]/internal/font/google/jetbrains_mono_db724454.js [app-rsc] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/constants.js [app-rsc] (ecmascript)");
;
;
;
;
;
const metadata = {
    title: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].tagline}`,
    description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description,
    keywords: [
        "civic reporting",
        "community transparency",
        "AI",
        "photo recognition",
        "social platform"
    ],
    authors: [
        {
            name: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].author
        }
    ],
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].author,
    publisher: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].author,
    formatDetection: {
        email: false,
        address: false,
        telephone: false
    },
    icons: {
        icon: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
        shortcut: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
        apple: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp"
    },
    manifest: "/manifest.json",
    openGraph: {
        type: "website",
        locale: "en_US",
        url: "https://messageyou.ai",
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].tagline}`,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description,
        siteName: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name,
        images: [
            {
                url: "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp",
                width: 1200,
                height: 630,
                alt: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name} Logo`
            }
        ]
    },
    twitter: {
        card: "summary_large_image",
        title: `${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].name} - ${__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].tagline}`,
        description: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$constants$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["APP_CONFIG"].description,
        images: [
            "https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp"
        ],
        creator: "@messageyou"
    },
    robots: {
        index: true,
        follow: true,
        googleBot: {
            index: true,
            follow: true,
            "max-video-preview": -1,
            "max-image-preview": "large",
            "max-snippet": -1
        }
    },
    verification: {
        google: "your-google-verification-code",
        yandex: "your-yandex-verification-code"
    }
};
const viewport = {
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    themeColor: [
        {
            media: "(prefers-color-scheme: light)",
            color: "#0ea5e9"
        },
        {
            media: "(prefers-color-scheme: dark)",
            color: "#0ea5e9"
        }
    ]
};
function RootLayout({ children }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("html", {
        lang: "en",
        suppressHydrationWarning: true,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("head", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "preconnect",
                        href: "https://fonts.googleapis.com"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.js",
                        lineNumber: 90,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                        rel: "preconnect",
                        href: "https://fonts.gstatic.com",
                        crossOrigin: "anonymous"
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.js",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/layout.js",
                lineNumber: 89,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("body", {
                className: `${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$inter_1091e993$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} ${__TURBOPACK__imported__module__$5b$next$5d2f$internal$2f$font$2f$google$2f$jetbrains_mono_db724454$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["default"].variable} font-sans antialiased min-h-screen bg-background text-foreground`,
                suppressHydrationWarning: true,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "relative flex min-h-screen flex-col",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$rsc$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$rsc$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex-1",
                        children: children
                    }, void 0, false, {
                        fileName: "[project]/src/app/layout.js",
                        lineNumber: 98,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/layout.js",
                    lineNumber: 97,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/layout.js",
                lineNumber: 93,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/layout.js",
        lineNumber: 88,
        columnNumber: 5
    }, this);
}
}}),
"[project]/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
module.exports = __turbopack_context__.r("[project]/node_modules/next/dist/server/route-modules/app-page/module.compiled.js [app-rsc] (ecmascript)").vendored['react-rsc'].ReactJsxDevRuntime; //# sourceMappingURL=react-jsx-dev-runtime.js.map
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__bc3b57b0._.js.map