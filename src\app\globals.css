@import "tailwindcss";

:root {
  /* Light theme colors */
  --background: #ffffff;
  --foreground: #0a0a0a;
  --card: #ffffff;
  --card-foreground: #0a0a0a;
  --popover: #ffffff;
  --popover-foreground: #0a0a0a;
  --primary: #0ea5e9;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f1f5f9;
  --muted-foreground: #64748b;
  --accent: #d946ef;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #0ea5e9;
  --radius: 0.5rem;

  /* Futuristic AI colors */
  --neon-blue: #00d4ff;
  --neon-purple: #b347d9;
  --neon-green: #39ff14;
  --neon-pink: #ff1493;
  --cyber-dark: #0a0a0a;
  --cyber-gray: #1a1a1a;
  --matrix-green: #00ff41;
  --hologram: rgba(0, 212, 255, 0.1);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ffffff;
    --card: #1a1a1a;
    --card-foreground: #ffffff;
    --popover: #1a1a1a;
    --popover-foreground: #ffffff;
    --primary: #0ea5e9;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #d946ef;
    --accent-foreground: #ffffff;
    --destructive: #ef4444;
    --destructive-foreground: #ffffff;
    --border: #334155;
    --input: #334155;
    --ring: #0ea5e9;
  }
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);

  /* Futuristic colors */
  --color-neon-blue: var(--neon-blue);
  --color-neon-purple: var(--neon-purple);
  --color-neon-green: var(--neon-green);
  --color-neon-pink: var(--neon-pink);
  --color-cyber-dark: var(--cyber-dark);
  --color-cyber-gray: var(--cyber-gray);
  --color-matrix-green: var(--matrix-green);
  --color-hologram: var(--hologram);

  /* Typography */
  --font-sans: 'Inter', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', monospace;

  /* Spacing & Sizing */
  --radius: var(--radius);

  /* Animations */
  --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  --animate-bounce: bounce 1s infinite;
  --animate-spin: spin 1s linear infinite;
  --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
  --animate-glow: glow 2s ease-in-out infinite alternate;
  --animate-float: float 3s ease-in-out infinite;
  --animate-matrix: matrix 20s linear infinite;
}

/* Global Styles */
* {
  border-color: var(--border);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Animations */
@keyframes glow {
  from {
    box-shadow: 0 0 5px var(--neon-blue), 0 0 10px var(--neon-blue), 0 0 15px var(--neon-blue);
  }
  to {
    box-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes matrix {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100vh);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Utility Classes */
.animate-glow {
  animation: var(--animate-glow);
}

.animate-float {
  animation: var(--animate-float);
}

.animate-matrix {
  animation: var(--animate-matrix);
}

.animate-slide-in-up {
  animation: slideInUp 0.6s ease-out;
}

.animate-fade-in-scale {
  animation: fadeInScale 0.5s ease-out;
}

/* Neon Effects */
.neon-border {
  border: 1px solid var(--neon-blue);
  box-shadow: 0 0 5px var(--neon-blue);
}

.neon-text {
  color: var(--neon-blue);
  text-shadow: 0 0 5px var(--neon-blue);
}

.neon-glow {
  box-shadow: 0 0 10px var(--neon-blue), 0 0 20px var(--neon-blue), 0 0 30px var(--neon-blue);
}

/* Glassmorphism */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
  background: rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Holographic Effect */
.holographic {
  background: linear-gradient(45deg, var(--neon-blue), var(--neon-purple), var(--neon-pink));
  background-size: 400% 400%;
  animation: gradient 3s ease infinite;
}

@keyframes gradient {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent);
}

/* Selection Styling */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}
