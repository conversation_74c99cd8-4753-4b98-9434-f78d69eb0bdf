{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/lib/utils.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...string} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Utility function to format numbers with commas\n * @param {number} num - Number to format\n * @returns {string} Formatted number\n */\nexport function formatNumber(num) {\n  return new Intl.NumberFormat().format(num);\n}\n\n/**\n * Utility function to generate random ID\n * @param {number} length - Length of the ID\n * @returns {string} Random ID\n */\nexport function generateId(length = 8) {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\n  let result = '';\n  for (let i = 0; i < length; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n}\n\n/**\n * Utility function to simulate API delay\n * @param {number} ms - Milliseconds to delay\n * @returns {Promise} Promise that resolves after delay\n */\nexport function delay(ms) {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\n/**\n * Utility function to simulate photo analysis\n * @param {File} file - Image file\n * @returns {Promise<Object>} Analysis result\n */\nexport async function analyzePhoto(file) {\n  await delay(2000); // Simulate processing time\n  \n  // Simulate AI analysis result\n  const entities = [\n    { type: 'person', confidence: 0.95, name: 'John Doe' },\n    { type: 'business', confidence: 0.87, name: 'Coffee Shop Downtown' },\n    { type: 'place', confidence: 0.92, name: 'Central Park' },\n    { type: 'vehicle', confidence: 0.89, name: 'Blue Honda Civic' },\n    { type: 'item', confidence: 0.78, name: 'Lost iPhone' }\n  ];\n  \n  const randomEntity = entities[Math.floor(Math.random() * entities.length)];\n  \n  return {\n    success: true,\n    entity: randomEntity,\n    processing_time: '2.3s',\n    timestamp: new Date().toISOString()\n  };\n}\n\n/**\n * Utility function to get entity color based on type\n * @param {string} type - Entity type\n * @returns {string} Tailwind color class\n */\nexport function getEntityColor(type) {\n  const colors = {\n    person: 'bg-blue-500',\n    business: 'bg-green-500',\n    place: 'bg-purple-500',\n    vehicle: 'bg-orange-500',\n    item: 'bg-pink-500'\n  };\n  return colors[type] || 'bg-gray-500';\n}\n\n/**\n * Utility function to format date\n * @param {string|Date} date - Date to format\n * @returns {string} Formatted date\n */\nexport function formatDate(date) {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  }).format(new Date(date));\n}\n\n/**\n * Utility function to calculate reputation score\n * @param {number} upVotes - Number of up votes\n * @param {number} downVotes - Number of down votes\n * @returns {number} Reputation score (0-5)\n */\nexport function calculateReputation(upVotes, downVotes) {\n  const total = upVotes + downVotes;\n  if (total === 0) return 3; // Default neutral score\n  \n  const ratio = upVotes / total;\n  return Math.round(ratio * 5 * 10) / 10; // Round to 1 decimal place\n}\n\n/**\n * Utility function to get reputation color\n * @param {number} score - Reputation score (0-5)\n * @returns {string} Tailwind color class\n */\nexport function getReputationColor(score) {\n  if (score >= 4) return 'text-green-500';\n  if (score >= 3) return 'text-yellow-500';\n  if (score >= 2) return 'text-orange-500';\n  return 'text-red-500';\n}\n\n/**\n * Utility function to simulate localStorage operations\n * @param {string} key - Storage key\n * @param {any} value - Value to store (optional)\n * @returns {any} Stored value or null\n */\nexport function storage(key, value) {\n  if (typeof window === 'undefined') return null;\n  \n  if (value !== undefined) {\n    localStorage.setItem(key, JSON.stringify(value));\n    return value;\n  }\n  \n  try {\n    const item = localStorage.getItem(key);\n    return item ? JSON.parse(item) : null;\n  } catch (error) {\n    console.error('Error parsing localStorage item:', error);\n    return null;\n  }\n}\n\n/**\n * Utility function to debounce function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Utility function to throttle function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Time limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAOO,SAAS,aAAa,GAAG;IAC9B,OAAO,IAAI,KAAK,YAAY,GAAG,MAAM,CAAC;AACxC;AAOO,SAAS,WAAW,SAAS,CAAC;IACnC,MAAM,QAAQ;IACd,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,IAAK;QAC/B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAOO,SAAS,MAAM,EAAE;IACtB,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAOO,eAAe,aAAa,IAAI;IACrC,MAAM,MAAM,OAAO,2BAA2B;IAE9C,8BAA8B;IAC9B,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,YAAY;YAAM,MAAM;QAAW;QACrD;YAAE,MAAM;YAAY,YAAY;YAAM,MAAM;QAAuB;QACnE;YAAE,MAAM;YAAS,YAAY;YAAM,MAAM;QAAe;QACxD;YAAE,MAAM;YAAW,YAAY;YAAM,MAAM;QAAmB;QAC9D;YAAE,MAAM;YAAQ,YAAY;YAAM,MAAM;QAAc;KACvD;IAED,MAAM,eAAe,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;IAE1E,OAAO;QACL,SAAS;QACT,QAAQ;QACR,iBAAiB;QACjB,WAAW,IAAI,OAAO,WAAW;IACnC;AACF;AAOO,SAAS,eAAe,IAAI;IACjC,MAAM,SAAS;QACb,QAAQ;QACR,UAAU;QACV,OAAO;QACP,SAAS;QACT,MAAM;IACR;IACA,OAAO,MAAM,CAAC,KAAK,IAAI;AACzB;AAOO,SAAS,WAAW,IAAI;IAC7B,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAQO,SAAS,oBAAoB,OAAO,EAAE,SAAS;IACpD,MAAM,QAAQ,UAAU;IACxB,IAAI,UAAU,GAAG,OAAO,GAAG,wBAAwB;IAEnD,MAAM,QAAQ,UAAU;IACxB,OAAO,KAAK,KAAK,CAAC,QAAQ,IAAI,MAAM,IAAI,2BAA2B;AACrE;AAOO,SAAS,mBAAmB,KAAK;IACtC,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO;AACT;AAQO,SAAS,QAAQ,GAAG,EAAE,KAAK;IAChC,wCAAmC,OAAO;;AAc5C;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,GAAG,IAAI;QACrB,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/components/ui/Button.js"], "sourcesContent": ["import { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Button = forwardRef(({ \n  className, \n  variant = 'default', \n  size = 'default', \n  asChild = false,\n  children,\n  ...props \n}, ref) => {\n  const Comp = asChild ? 'span' : 'button';\n  \n  return (\n    <Comp\n      className={cn(buttonVariants({ variant, size, className }))}\n      ref={ref}\n      {...props}\n    >\n      {children}\n    </Comp>\n  );\n});\n\nButton.displayName = 'Button';\n\nconst buttonVariants = ({ variant, size, className }) => {\n  const baseStyles = 'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 hover:scale-105 active:scale-95';\n  \n  const variants = {\n    default: 'bg-primary text-primary-foreground hover:bg-primary/90 shadow-lg hover:shadow-xl',\n    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-lg hover:shadow-xl',\n    outline: 'border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-md hover:shadow-lg',\n    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-md hover:shadow-lg',\n    ghost: 'hover:bg-accent hover:text-accent-foreground',\n    link: 'text-primary underline-offset-4 hover:underline',\n    neon: 'bg-transparent border-2 border-neon-blue text-neon-blue hover:bg-neon-blue hover:text-black neon-glow hover:animate-glow',\n    holographic: 'holographic text-white border-0 shadow-2xl hover:shadow-neon-blue/50',\n    glass: 'glass text-foreground hover:bg-white/20 border border-white/30',\n    cyber: 'bg-cyber-dark text-neon-green border border-neon-green hover:bg-neon-green hover:text-cyber-dark transition-all duration-300'\n  };\n  \n  const sizes = {\n    default: 'h-10 px-4 py-2',\n    sm: 'h-9 rounded-md px-3',\n    lg: 'h-11 rounded-md px-8',\n    xl: 'h-12 rounded-lg px-10 text-base',\n    icon: 'h-10 w-10'\n  };\n  \n  return cn(\n    baseStyles,\n    variants[variant] || variants.default,\n    sizes[size] || sizes.default,\n    className\n  );\n};\n\nexport { Button, buttonVariants };\nexport default Button;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;;AAEA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EACzB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,SAAS,EAChB,UAAU,KAAK,EACf,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,OAAO,UAAU,SAAS;IAEhC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;kBAER;;;;;;AAGP;AAEA,OAAO,WAAW,GAAG;AAErB,MAAM,iBAAiB,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE;IAClD,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,aAAa;QACb,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ,SAAS;QACT,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,YACA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO,EACrC,KAAK,CAAC,KAAK,IAAI,MAAM,OAAO,EAC5B;AAEJ;;uCAGe", "debugId": null}}, {"offset": {"line": 234, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/lib/constants.js"], "sourcesContent": ["// App Configuration\nexport const APP_CONFIG = {\n  name: 'MessageY<PERSON>',\n  tagline: 'Snap. Post. Expose. Remember.',\n  description: 'Visual-first platform for civic reporting and community transparency',\n  version: '1.0.0',\n  author: 'MessageYou Team'\n};\n\n// API Configuration (Simulated)\nexport const API_CONFIG = {\n  baseUrl: '/api',\n  timeout: 5000,\n  retries: 3\n};\n\n// Entity Types\nexport const ENTITY_TYPES = {\n  PERSON: 'person',\n  BUSINESS: 'business',\n  PLACE: 'place',\n  VEHICLE: 'vehicle',\n  ITEM: 'item',\n  EVENT: 'event'\n};\n\n// Post Tags\nexport const POST_TAGS = {\n  REVIEW: 'review',\n  SCAM: 'scam',\n  LOST: 'lost',\n  FOUND: 'found',\n  GOOD_DEED: 'good_deed',\n  WARNING: 'warning',\n  QUESTION: 'question',\n  UPDATE: 'update'\n};\n\n// Reputation Levels\nexport const REPUTATION_LEVELS = {\n  EXCELLENT: { min: 4.5, label: 'Excellent', color: 'green' },\n  GOOD: { min: 3.5, label: 'Good', color: 'blue' },\n  AVERAGE: { min: 2.5, label: 'Average', color: 'yellow' },\n  POOR: { min: 1.5, label: 'Poor', color: 'orange' },\n  TERRIBLE: { min: 0, label: 'Terrible', color: 'red' }\n};\n\n// Animation Durations\nexport const ANIMATION_DURATION = {\n  FAST: 0.3,\n  NORMAL: 0.5,\n  SLOW: 0.8,\n  VERY_SLOW: 1.2\n};\n\n// Breakpoints (matching Tailwind)\nexport const BREAKPOINTS = {\n  SM: 640,\n  MD: 768,\n  LG: 1024,\n  XL: 1280,\n  '2XL': 1536\n};\n\n// Color Palette (Futuristic AI Theme)\nexport const COLORS = {\n  primary: {\n    50: '#f0f9ff',\n    100: '#e0f2fe',\n    200: '#bae6fd',\n    300: '#7dd3fc',\n    400: '#38bdf8',\n    500: '#0ea5e9',\n    600: '#0284c7',\n    700: '#0369a1',\n    800: '#075985',\n    900: '#0c4a6e'\n  },\n  accent: {\n    50: '#fdf4ff',\n    100: '#fae8ff',\n    200: '#f5d0fe',\n    300: '#f0abfc',\n    400: '#e879f9',\n    500: '#d946ef',\n    600: '#c026d3',\n    700: '#a21caf',\n    800: '#86198f',\n    900: '#701a75'\n  },\n  success: {\n    50: '#f0fdf4',\n    100: '#dcfce7',\n    200: '#bbf7d0',\n    300: '#86efac',\n    400: '#4ade80',\n    500: '#22c55e',\n    600: '#16a34a',\n    700: '#15803d',\n    800: '#166534',\n    900: '#14532d'\n  },\n  warning: {\n    50: '#fffbeb',\n    100: '#fef3c7',\n    200: '#fde68a',\n    300: '#fcd34d',\n    400: '#fbbf24',\n    500: '#f59e0b',\n    600: '#d97706',\n    700: '#b45309',\n    800: '#92400e',\n    900: '#78350f'\n  },\n  danger: {\n    50: '#fef2f2',\n    100: '#fee2e2',\n    200: '#fecaca',\n    300: '#fca5a5',\n    400: '#f87171',\n    500: '#ef4444',\n    600: '#dc2626',\n    700: '#b91c1c',\n    800: '#991b1b',\n    900: '#7f1d1d'\n  }\n};\n\n// Demo Data Configuration\nexport const DEMO_CONFIG = {\n  maxEntities: 50,\n  maxPostsPerEntity: 20,\n  maxCommentsPerPost: 10,\n  simulationDelay: 2000,\n  typingSpeed: 50\n};\n\n// Feature Flags\nexport const FEATURES = {\n  PHOTO_UPLOAD: true,\n  AI_ANALYSIS: true,\n  REAL_TIME_UPDATES: true,\n  NOTIFICATIONS: true,\n  DARK_MODE: true,\n  ANALYTICS: false,\n  BETA_FEATURES: true\n};\n\n// Navigation Menu Items\nexport const NAVIGATION = [\n  { name: 'Home', href: '/', icon: 'Home' },\n  { name: 'Demo', href: '/demo', icon: 'Play' },\n  { name: 'Pitch', href: '/pitch', icon: 'Presentation' },\n  { name: 'Why Us', href: '/why-us', icon: 'Users' },\n  { name: 'Roadmap', href: '/roadmap', icon: 'Map' },\n  { name: 'Sign Up', href: '/signup', icon: 'UserPlus' }\n];\n\n// Social Media Links\nexport const SOCIAL_LINKS = [\n  { name: 'Twitter', href: '#', icon: 'Twitter' },\n  { name: 'LinkedIn', href: '#', icon: 'Linkedin' },\n  { name: 'GitHub', href: '#', icon: 'Github' },\n  { name: 'Discord', href: '#', icon: 'MessageCircle' }\n];\n\n// Pricing Plans\nexport const PRICING_PLANS = [\n  {\n    name: 'Free',\n    price: 0,\n    period: 'forever',\n    description: 'Perfect for getting started',\n    features: [\n      'Basic photo upload',\n      'View public posts',\n      'Basic search',\n      'Limited posts (10/month)',\n      'Community support'\n    ],\n    cta: 'Get Started',\n    popular: false\n  },\n  {\n    name: 'Premium',\n    price: 4.99,\n    period: 'month',\n    description: 'For active community members',\n    features: [\n      'Unlimited posts',\n      'Advanced AI analysis',\n      'Priority search results',\n      'Trend alerts',\n      'Enhanced privacy',\n      'Email support'\n    ],\n    cta: 'Start Free Trial',\n    popular: true\n  },\n  {\n    name: 'Business',\n    price: 19.99,\n    period: 'month',\n    description: 'For businesses and organizations',\n    features: [\n      'Entity claiming',\n      'Business dashboard',\n      'Response tools',\n      'Analytics & insights',\n      'API access',\n      'Priority support'\n    ],\n    cta: 'Contact Sales',\n    popular: false\n  }\n];\n\n// Testimonials\nexport const TESTIMONIALS = [\n  {\n    name: 'Sarah Chen',\n    role: 'Community Organizer',\n    avatar: '/images/avatars/sarah.jpg',\n    content: 'MessageYou helped our neighborhood identify and stop a series of scams. The visual-first approach makes reporting so much easier.',\n    rating: 5\n  },\n  {\n    name: 'Mike Rodriguez',\n    role: 'Small Business Owner',\n    avatar: '/images/avatars/mike.jpg',\n    content: 'Being able to respond to community feedback directly has improved our customer relationships tremendously.',\n    rating: 5\n  },\n  {\n    name: 'Dr. Emily Watson',\n    role: 'Urban Planning Researcher',\n    avatar: '/images/avatars/emily.jpg',\n    content: 'This platform provides invaluable data for understanding community dynamics and safety patterns.',\n    rating: 5\n  }\n];\n\n// FAQ Data\nexport const FAQ_DATA = [\n  {\n    question: 'How does the photo recognition work?',\n    answer: 'Our AI analyzes uploaded photos to identify people, places, businesses, and objects, then matches them to existing entities in our database or creates new ones.'\n  },\n  {\n    question: 'Is my personal information safe?',\n    answer: 'We prioritize privacy with optional anonymous posting, data encryption, and strict compliance with privacy regulations like GDPR and CCPA.'\n  },\n  {\n    question: 'Can businesses respond to posts about them?',\n    answer: 'Yes, verified businesses can claim their entities and respond to posts, providing transparency and accountability for all parties.'\n  },\n  {\n    question: 'How do you prevent false reports?',\n    answer: 'We use AI-powered content moderation, community voting, and human review to identify and filter out false or malicious content.'\n  },\n  {\n    question: 'What types of entities can be reported?',\n    answer: 'You can report on people, businesses, places, vehicles, lost items, events, and more. If it exists in the real world, it can be documented on MessageYou.'\n  }\n];\n\n// Animation Presets\nexport const ANIMATION_PRESETS = {\n  fadeIn: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n    transition: { duration: 0.5 }\n  },\n  slideUp: {\n    initial: { opacity: 0, y: 50 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  },\n  slideDown: {\n    initial: { opacity: 0, y: -50 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  },\n  scaleIn: {\n    initial: { opacity: 0, scale: 0.8 },\n    animate: { opacity: 1, scale: 1 },\n    transition: { duration: 0.5 }\n  },\n  rotateIn: {\n    initial: { opacity: 0, rotate: -10 },\n    animate: { opacity: 1, rotate: 0 },\n    transition: { duration: 0.7 }\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;;;;;;;AACb,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAGO,MAAM,aAAa;IACxB,SAAS;IACT,SAAS;IACT,SAAS;AACX;AAGO,MAAM,eAAe;IAC1B,QAAQ;IACR,UAAU;IACV,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;AACT;AAGO,MAAM,YAAY;IACvB,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,WAAW;IACX,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAGO,MAAM,oBAAoB;IAC/B,WAAW;QAAE,KAAK;QAAK,OAAO;QAAa,OAAO;IAAQ;IAC1D,MAAM;QAAE,KAAK;QAAK,OAAO;QAAQ,OAAO;IAAO;IAC/C,SAAS;QAAE,KAAK;QAAK,OAAO;QAAW,OAAO;IAAS;IACvD,MAAM;QAAE,KAAK;QAAK,OAAO;QAAQ,OAAO;IAAS;IACjD,UAAU;QAAE,KAAK;QAAG,OAAO;QAAY,OAAO;IAAM;AACtD;AAGO,MAAM,qBAAqB;IAChC,MAAM;IACN,QAAQ;IACR,MAAM;IACN,WAAW;AACb;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,cAAc;IACzB,aAAa;IACb,mBAAmB;IACnB,oBAAoB;IACpB,iBAAiB;IACjB,aAAa;AACf;AAGO,MAAM,WAAW;IACtB,cAAc;IACd,aAAa;IACb,mBAAmB;IACnB,eAAe;IACf,WAAW;IACX,WAAW;IACX,eAAe;AACjB;AAGO,MAAM,aAAa;IACxB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM;IAAO;IACxC;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM;IAAe;IACtD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM;IAAQ;IACjD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM;IAAM;IACjD;QAAE,MAAM;QAAW,MAAM;QAAW,MAAM;IAAW;CACtD;AAGM,MAAM,eAAe;IAC1B;QAAE,MAAM;QAAW,MAAM;QAAK,MAAM;IAAU;IAC9C;QAAE,MAAM;QAAY,MAAM;QAAK,MAAM;IAAW;IAChD;QAAE,MAAM;QAAU,MAAM;QAAK,MAAM;IAAS;IAC5C;QAAE,MAAM;QAAW,MAAM;QAAK,MAAM;IAAgB;CACrD;AAGM,MAAM,gBAAgB;IAC3B;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;CACD;AAGM,MAAM,eAAe;IAC1B;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAGM,MAAM,WAAW;IACtB;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAGM,MAAM,oBAAoB;IAC/B,QAAQ;QACN,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,WAAW;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,UAAU;QACR,SAAS;YAAE,SAAS;YAAG,QAAQ,CAAC;QAAG;QACnC,SAAS;YAAE,SAAS;YAAG,QAAQ;QAAE;QACjC,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/components/sections/Navigation.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport Link from 'next/link';\nimport { Button } from '@/components/ui/Button';\nimport { APP_CONFIG, NAVIGATION } from '@/lib/constants';\nimport { Menu, X, Zap } from 'lucide-react';\nimport { cn } from '@/lib/utils';\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  // Handle scroll effect\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  // Close mobile menu when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event) => {\n      if (isOpen && !event.target.closest('.mobile-menu')) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('click', handleClickOutside);\n    return () => document.removeEventListener('click', handleClickOutside);\n  }, [isOpen]);\n\n  return (\n    <nav className={cn(\n      'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n      isScrolled \n        ? 'bg-background/80 backdrop-blur-md border-b border-border shadow-lg' \n        : 'bg-transparent'\n    )}>\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          \n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-3 group\">\n            <div className=\"relative\">\n              <div className=\"w-10 h-10 bg-gradient-to-br from-primary to-accent rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300\">\n                <Zap className=\"w-6 h-6 text-white\" />\n              </div>\n              <div className=\"absolute inset-0 bg-gradient-to-br from-primary to-accent rounded-lg blur-lg opacity-30 group-hover:opacity-50 transition-opacity duration-300\" />\n            </div>\n            <div className=\"hidden sm:block\">\n              <div className=\"text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent\">\n                {APP_CONFIG.name}\n              </div>\n              <div className=\"text-xs text-muted-foreground -mt-1\">\n                {APP_CONFIG.tagline}\n              </div>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-8\">\n            {NAVIGATION.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"relative text-foreground hover:text-primary transition-colors duration-200 group\"\n              >\n                <span className=\"relative z-10\">{item.name}</span>\n                <div className=\"absolute inset-x-0 -bottom-1 h-0.5 bg-primary scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left\" />\n              </Link>\n            ))}\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <Button variant=\"ghost\" size=\"sm\">\n              Sign In\n            </Button>\n            <Button variant=\"neon\" size=\"sm\" className=\"group\">\n              <Zap className=\"w-4 h-4 mr-2 group-hover:animate-pulse\" />\n              Get Started\n            </Button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"lg:hidden p-2 rounded-lg hover:bg-muted transition-colors duration-200\"\n            aria-label=\"Toggle menu\"\n          >\n            {isOpen ? (\n              <X className=\"w-6 h-6\" />\n            ) : (\n              <Menu className=\"w-6 h-6\" />\n            )}\n          </button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <div className={cn(\n          'mobile-menu lg:hidden overflow-hidden transition-all duration-300 ease-in-out',\n          isOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n        )}>\n          <div className=\"py-4 space-y-2 border-t border-border\">\n            {NAVIGATION.map((item, index) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                onClick={() => setIsOpen(false)}\n                className={cn(\n                  'block px-4 py-3 rounded-lg text-foreground hover:text-primary hover:bg-muted transition-all duration-200',\n                  'animate-slide-in-up'\n                )}\n                style={{ animationDelay: `${index * 100}ms` }}\n              >\n                {item.name}\n              </Link>\n            ))}\n            \n            {/* Mobile CTA */}\n            <div className=\"pt-4 space-y-2\">\n              <Button \n                variant=\"ghost\" \n                size=\"sm\" \n                className=\"w-full justify-start\"\n                onClick={() => setIsOpen(false)}\n              >\n                Sign In\n              </Button>\n              <Button \n                variant=\"neon\" \n                size=\"sm\" \n                className=\"w-full group\"\n                onClick={() => setIsOpen(false)}\n              >\n                <Zap className=\"w-4 h-4 mr-2 group-hover:animate-pulse\" />\n                Get Started\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,cAAc,OAAO,OAAO,GAAG;QACjC;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,CAAC,MAAM,MAAM,CAAC,OAAO,CAAC,iBAAiB;gBACnD,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,SAAS;QACnC,OAAO,IAAM,SAAS,mBAAmB,CAAC,SAAS;IACrD,GAAG;QAAC;KAAO;IAEX,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,+DACA,aACI,uEACA;kBAEJ,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;sDAEjB,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACZ,uHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;sDAElB,8OAAC;4CAAI,WAAU;sDACZ,uHAAA,CAAA,aAAU,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAMzB,8OAAC;4BAAI,WAAU;sCACZ,uHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,qBACf,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC;4CAAK,WAAU;sDAAiB,KAAK,IAAI;;;;;;sDAC1C,8OAAC;4CAAI,WAAU;;;;;;;mCALV,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;8CAAK;;;;;;8CAGlC,8OAAC,iIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAO,MAAK;oCAAK,WAAU;;sDACzC,8OAAC,gMAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;wCAA2C;;;;;;;;;;;;;sCAM9D,8OAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;4BACV,cAAW;sCAEV,uBACC,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAEb,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAMtB,8OAAC;oBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,iFACA,SAAS,yBAAyB;8BAElC,cAAA,8OAAC;wBAAI,WAAU;;4BACZ,uHAAA,CAAA,aAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACrB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,UAAU;oCACzB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4GACA;oCAEF,OAAO;wCAAE,gBAAgB,GAAG,QAAQ,IAAI,EAAE,CAAC;oCAAC;8CAE3C,KAAK,IAAI;mCATL,KAAK,IAAI;;;;;0CAclB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,UAAU;kDAC1B;;;;;;kDAGD,8OAAC,iIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,UAAU;;0DAEzB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1E", "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/components/ui/Card.js"], "sourcesContent": ["import { forwardRef } from 'react';\nimport { cn } from '@/lib/utils';\n\nconst Card = forwardRef(({ className, variant = 'default', ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(cardVariants({ variant }), className)}\n    {...props}\n  />\n));\nCard.displayName = 'Card';\n\nconst CardHeader = forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex flex-col space-y-1.5 p-6', className)}\n    {...props}\n  />\n));\nCardHeader.displayName = 'CardHeader';\n\nconst CardTitle = forwardRef(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      'text-2xl font-semibold leading-none tracking-tight',\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = 'CardTitle';\n\nconst CardDescription = forwardRef(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn('text-sm text-muted-foreground', className)}\n    {...props}\n  />\n));\nCardDescription.displayName = 'CardDescription';\n\nconst CardContent = forwardRef(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn('p-6 pt-0', className)} {...props} />\n));\nCardContent.displayName = 'CardContent';\n\nconst CardFooter = forwardRef(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn('flex items-center p-6 pt-0', className)}\n    {...props}\n  />\n));\nCardFooter.displayName = 'CardFooter';\n\nconst cardVariants = ({ variant }) => {\n  const baseStyles = 'rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300 hover:shadow-lg';\n  \n  const variants = {\n    default: 'border-border',\n    glass: 'glass border-white/20 backdrop-blur-md',\n    neon: 'border-neon-blue neon-border bg-transparent',\n    cyber: 'bg-cyber-gray border-neon-green/30 hover:border-neon-green',\n    holographic: 'holographic border-0 text-white',\n    elevated: 'shadow-xl hover:shadow-2xl border-0 bg-gradient-to-br from-card to-card/80',\n    minimal: 'border-0 shadow-none bg-transparent',\n    floating: 'shadow-2xl hover:shadow-neon-blue/20 border-neon-blue/20 animate-float'\n  };\n  \n  return cn(\n    baseStyles,\n    variants[variant] || variants.default\n  );\n};\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n  cardVariants\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,GAAG,OAAO,EAAE,oBACrE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAE;QAAQ,IAAI;QACxC,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACrD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC3D,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACvD,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBACtD,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,eAAe,CAAC,EAAE,OAAO,EAAE;IAC/B,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf,SAAS;QACT,OAAO;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,UAAU;QACV,SAAS;QACT,UAAU;IACZ;IAEA,OAAO,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACN,YACA,QAAQ,CAAC,QAAQ,IAAI,SAAS,OAAO;AAEzC;;uCAYe", "debugId": null}}, {"offset": {"line": 1020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef, useState } from 'react';\nimport { Button } from '@/components/ui/Button';\nimport { Card } from '@/components/ui/Card';\nimport { APP_CONFIG } from '@/lib/constants';\nimport { Camera, Upload, Zap, Users, Shield, Sparkles } from 'lucide-react';\n\nexport default function HeroSection() {\n  const canvasRef = useRef(null);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [currentDemo, setCurrentDemo] = useState(0);\n\n  // Demo scenarios\n  const demoScenarios = [\n    {\n      title: \"Scam Alert\",\n      description: \"Photo reveals known scammer\",\n      icon: Shield,\n      color: \"text-red-400\"\n    },\n    {\n      title: \"Good Deed\",\n      description: \"Community hero recognized\",\n      icon: Sparkles,\n      color: \"text-green-400\"\n    },\n    {\n      title: \"Lost & Found\",\n      description: \"Missing item located\",\n      icon: Users,\n      color: \"text-blue-400\"\n    }\n  ];\n\n  // Particle animation effect\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    const particles = [];\n    const particleCount = 50;\n\n    // Set canvas size\n    const resizeCanvas = () => {\n      canvas.width = canvas.offsetWidth;\n      canvas.height = canvas.offsetHeight;\n    };\n    resizeCanvas();\n    window.addEventListener('resize', resizeCanvas);\n\n    // Create particles\n    for (let i = 0; i < particleCount; i++) {\n      particles.push({\n        x: Math.random() * canvas.width,\n        y: Math.random() * canvas.height,\n        vx: (Math.random() - 0.5) * 0.5,\n        vy: (Math.random() - 0.5) * 0.5,\n        size: Math.random() * 2 + 1,\n        opacity: Math.random() * 0.5 + 0.2\n      });\n    }\n\n    // Animation loop\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n      \n      particles.forEach(particle => {\n        // Update position\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Wrap around edges\n        if (particle.x < 0) particle.x = canvas.width;\n        if (particle.x > canvas.width) particle.x = 0;\n        if (particle.y < 0) particle.y = canvas.height;\n        if (particle.y > canvas.height) particle.y = 0;\n\n        // Draw particle\n        ctx.beginPath();\n        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n        ctx.fillStyle = `rgba(14, 165, 233, ${particle.opacity})`;\n        ctx.fill();\n\n        // Draw connections\n        particles.forEach(otherParticle => {\n          const dx = particle.x - otherParticle.x;\n          const dy = particle.y - otherParticle.y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < 100) {\n            ctx.beginPath();\n            ctx.moveTo(particle.x, particle.y);\n            ctx.lineTo(otherParticle.x, otherParticle.y);\n            ctx.strokeStyle = `rgba(14, 165, 233, ${0.1 * (1 - distance / 100)})`;\n            ctx.lineWidth = 0.5;\n            ctx.stroke();\n          }\n        });\n      });\n\n      requestAnimationFrame(animate);\n    };\n\n    animate();\n    setIsLoaded(true);\n\n    return () => {\n      window.removeEventListener('resize', resizeCanvas);\n    };\n  }, []);\n\n  // Cycle through demo scenarios\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentDemo((prev) => (prev + 1) % demoScenarios.length);\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const currentScenario = demoScenarios[currentDemo];\n  const IconComponent = currentScenario.icon;\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-background to-primary/5\">\n      {/* Animated Background */}\n      <canvas\n        ref={canvasRef}\n        className=\"absolute inset-0 w-full h-full opacity-30\"\n        style={{ background: 'transparent' }}\n      />\n      \n      {/* Background Gradient Orbs */}\n      <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse\" />\n      <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000\" />\n      \n      {/* Main Content */}\n      <div className=\"relative z-10 container mx-auto px-4 py-20\">\n        <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n          \n          {/* Left Column - Text Content */}\n          <div className={`space-y-8 ${isLoaded ? 'animate-slide-in-up' : 'opacity-0'}`}>\n            <div className=\"space-y-4\">\n              <div className=\"inline-flex items-center px-4 py-2 rounded-full bg-primary/10 border border-primary/20 text-primary text-sm font-medium\">\n                <Zap className=\"w-4 h-4 mr-2\" />\n                AI-Powered Civic Platform\n              </div>\n              \n              <h1 className=\"text-5xl lg:text-7xl font-bold leading-tight\">\n                <span className=\"bg-gradient-to-r from-primary via-accent to-primary bg-clip-text text-transparent\">\n                  {APP_CONFIG.name}\n                </span>\n              </h1>\n              \n              <p className=\"text-xl lg:text-2xl text-muted-foreground font-light\">\n                {APP_CONFIG.tagline}\n              </p>\n              \n              <p className=\"text-lg text-muted-foreground max-w-xl\">\n                Snap a photo of anyone or anything to instantly access community reports, \n                warnings, and real-world reputation. No names needed—just point, post, and protect.\n              </p>\n            </div>\n\n            {/* CTA Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Button \n                size=\"xl\" \n                variant=\"neon\"\n                className=\"group\"\n              >\n                <Camera className=\"w-5 h-5 mr-2 group-hover:animate-pulse\" />\n                Try Demo Now\n              </Button>\n              \n              <Button \n                size=\"xl\" \n                variant=\"glass\"\n                className=\"group\"\n              >\n                <Upload className=\"w-5 h-5 mr-2 group-hover:animate-bounce\" />\n                Upload Photo\n              </Button>\n            </div>\n\n            {/* Stats */}\n            <div className=\"grid grid-cols-3 gap-6 pt-8\">\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-primary\">50K+</div>\n                <div className=\"text-sm text-muted-foreground\">Reports Filed</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-accent\">98%</div>\n                <div className=\"text-sm text-muted-foreground\">Accuracy Rate</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"text-2xl font-bold text-neon-green\">24/7</div>\n                <div className=\"text-sm text-muted-foreground\">AI Monitoring</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Right Column - Interactive Demo */}\n          <div className={`${isLoaded ? 'animate-fade-in-scale delay-300' : 'opacity-0'}`}>\n            <Card variant=\"glass\" className=\"p-8 space-y-6 hover:shadow-2xl transition-all duration-500\">\n              <div className=\"text-center space-y-4\">\n                <h3 className=\"text-2xl font-semibold\">Live Demo</h3>\n                <p className=\"text-muted-foreground\">\n                  See how MessageYou works in real-time\n                </p>\n              </div>\n\n              {/* Demo Phone Mockup */}\n              <div className=\"relative mx-auto w-64 h-96 bg-gradient-to-b from-cyber-dark to-cyber-gray rounded-3xl p-4 shadow-2xl\">\n                {/* Phone Screen */}\n                <div className=\"w-full h-full bg-background rounded-2xl p-4 relative overflow-hidden\">\n                  {/* Demo Content */}\n                  <div className=\"space-y-4\">\n                    {/* Camera Viewfinder */}\n                    <div className=\"aspect-square bg-gradient-to-br from-primary/20 to-accent/20 rounded-xl flex items-center justify-center relative\">\n                      <Camera className=\"w-12 h-12 text-primary animate-pulse\" />\n                      \n                      {/* Scanning Animation */}\n                      <div className=\"absolute inset-0 border-2 border-neon-blue rounded-xl\">\n                        <div className=\"absolute top-0 left-0 w-full h-1 bg-neon-blue animate-pulse\" />\n                        <div className=\"absolute bottom-0 left-0 w-full h-1 bg-neon-blue animate-pulse delay-500\" />\n                      </div>\n                    </div>\n\n                    {/* Analysis Result */}\n                    <Card variant=\"cyber\" className=\"p-4 space-y-3\">\n                      <div className=\"flex items-center space-x-3\">\n                        <div className={`p-2 rounded-full bg-current/10 ${currentScenario.color}`}>\n                          <IconComponent className={`w-5 h-5 ${currentScenario.color}`} />\n                        </div>\n                        <div>\n                          <div className=\"font-semibold\">{currentScenario.title}</div>\n                          <div className=\"text-sm text-muted-foreground\">\n                            {currentScenario.description}\n                          </div>\n                        </div>\n                      </div>\n                      \n                      {/* Progress Bar */}\n                      <div className=\"w-full bg-muted rounded-full h-2\">\n                        <div \n                          className=\"bg-primary h-2 rounded-full transition-all duration-1000\"\n                          style={{ width: '85%' }}\n                        />\n                      </div>\n                      \n                      <div className=\"text-xs text-muted-foreground\">\n                        Confidence: 85% • Processing: 1.2s\n                      </div>\n                    </Card>\n\n                    {/* Action Buttons */}\n                    <div className=\"grid grid-cols-2 gap-2\">\n                      <Button size=\"sm\" variant=\"neon\" className=\"text-xs\">\n                        View Reports\n                      </Button>\n                      <Button size=\"sm\" variant=\"glass\" className=\"text-xs\">\n                        Add Report\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Phone Details */}\n                <div className=\"absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-1 bg-muted rounded-full\" />\n                <div className=\"absolute bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 border-2 border-muted rounded-full\" />\n              </div>\n\n              {/* Demo Indicators */}\n              <div className=\"flex justify-center space-x-2\">\n                {demoScenarios.map((_, index) => (\n                  <div\n                    key={index}\n                    className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                      index === currentDemo ? 'bg-primary w-6' : 'bg-muted'\n                    }`}\n                  />\n                ))}\n              </div>\n            </Card>\n          </div>\n        </div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n        <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n          <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\" />\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,iBAAiB;IACjB,MAAM,gBAAgB;QACpB;YACE,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,OAAO;YACP,aAAa;YACb,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;QACT;KACD;IAED,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,MAAM,YAAY,EAAE;QACpB,MAAM,gBAAgB;QAEtB,kBAAkB;QAClB,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,WAAW;YACjC,OAAO,MAAM,GAAG,OAAO,YAAY;QACrC;QACA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,mBAAmB;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;YACtC,UAAU,IAAI,CAAC;gBACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;gBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;gBAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;gBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;YACjC;QACF;QAEA,iBAAiB;QACjB,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C,UAAU,OAAO,CAAC,CAAA;gBAChB,kBAAkB;gBAClB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBAEzB,oBAAoB;gBACpB,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,KAAK;gBAC7C,IAAI,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE,SAAS,CAAC,GAAG;gBAC5C,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,CAAC,GAAG,OAAO,MAAM;gBAC9C,IAAI,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE,SAAS,CAAC,GAAG;gBAE7C,gBAAgB;gBAChB,IAAI,SAAS;gBACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;gBAC5D,IAAI,SAAS,GAAG,CAAC,mBAAmB,EAAE,SAAS,OAAO,CAAC,CAAC,CAAC;gBACzD,IAAI,IAAI;gBAER,mBAAmB;gBACnB,UAAU,OAAO,CAAC,CAAA;oBAChB,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oBACvC,MAAM,KAAK,SAAS,CAAC,GAAG,cAAc,CAAC;oBACvC,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,KAAK;wBAClB,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;wBACjC,IAAI,MAAM,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC;wBAC3C,IAAI,WAAW,GAAG,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,WAAW,GAAG,EAAE,CAAC,CAAC;wBACrE,IAAI,SAAS,GAAG;wBAChB,IAAI,MAAM;oBACZ;gBACF;YACF;YAEA,sBAAsB;QACxB;QAEA;QACA,YAAY;QAEZ,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;QACvC;IACF,GAAG,EAAE;IAEL,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,eAAe,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,cAAc,MAAM;QAC5D,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,kBAAkB,aAAa,CAAC,YAAY;IAClD,MAAM,gBAAgB,gBAAgB,IAAI;IAE1C,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAc;;;;;;0BAIrC,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAGb,8OAAC;4BAAI,WAAW,CAAC,UAAU,EAAE,WAAW,wBAAwB,aAAa;;8CAC3E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAIlC,8OAAC;4CAAG,WAAU;sDACZ,cAAA,8OAAC;gDAAK,WAAU;0DACb,uHAAA,CAAA,aAAU,CAAC,IAAI;;;;;;;;;;;sDAIpB,8OAAC;4CAAE,WAAU;sDACV,uHAAA,CAAA,aAAU,CAAC,OAAO;;;;;;sDAGrB,8OAAC;4CAAE,WAAU;sDAAyC;;;;;;;;;;;;8CAOxD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA2C;;;;;;;sDAI/D,8OAAC,iIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;;8DAEV,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAA4C;;;;;;;;;;;;;8CAMlE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAkC;;;;;;8DACjD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAiC;;;;;;8DAChD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAqC;;;;;;8DACpD,8OAAC;oDAAI,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAMrD,8OAAC;4BAAI,WAAW,GAAG,WAAW,oCAAoC,aAAa;sCAC7E,cAAA,8OAAC,+HAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAQ,WAAU;;kDAC9B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAMvC,8OAAC;wCAAI,WAAU;;0DAEb,8OAAC;gDAAI,WAAU;0DAEb,cAAA,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAGlB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC;4EAAI,WAAU;;;;;;;;;;;;;;;;;;sEAKnB,8OAAC,+HAAA,CAAA,OAAI;4DAAC,SAAQ;4DAAQ,WAAU;;8EAC9B,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAW,CAAC,+BAA+B,EAAE,gBAAgB,KAAK,EAAE;sFACvE,cAAA,8OAAC;gFAAc,WAAW,CAAC,QAAQ,EAAE,gBAAgB,KAAK,EAAE;;;;;;;;;;;sFAE9D,8OAAC;;8FACC,8OAAC;oFAAI,WAAU;8FAAiB,gBAAgB,KAAK;;;;;;8FACrD,8OAAC;oFAAI,WAAU;8FACZ,gBAAgB,WAAW;;;;;;;;;;;;;;;;;;8EAMlC,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEACC,WAAU;wEACV,OAAO;4EAAE,OAAO;wEAAM;;;;;;;;;;;8EAI1B,8OAAC;oEAAI,WAAU;8EAAgC;;;;;;;;;;;;sEAMjD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAO,WAAU;8EAAU;;;;;;8EAGrD,8OAAC,iIAAA,CAAA,SAAM;oEAAC,MAAK;oEAAK,SAAQ;oEAAQ,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;;0DAQ5D,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;;;;;;;;;;;kDAIjB,8OAAC;wCAAI,WAAU;kDACZ,cAAc,GAAG,CAAC,CAAC,GAAG,sBACrB,8OAAC;gDAEC,WAAW,CAAC,iDAAiD,EAC3D,UAAU,cAAc,mBAAmB,YAC3C;+CAHG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAanB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}]}