{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_cf836d14.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_cf836d14-module__bs8O-G__className\",\n  \"variable\": \"inter_cf836d14-module__bs8O-G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_cf836d14.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-sans%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_968d15bb.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jetbrains_mono_968d15bb-module__KjorJW__className\",\n  \"variable\": \"jetbrains_mono_968d15bb-module__KjorJW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jetbrains_mono_968d15bb.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.js%22,%22import%22:%22JetBrains_Mono%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-mono%22}],%22variableName%22:%22jetbrainsMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'JetBrains Mono', 'JetBrains Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/lib/constants.js"], "sourcesContent": ["// App Configuration\nexport const APP_CONFIG = {\n  name: 'MessageY<PERSON>',\n  tagline: 'Snap. Post. Expose. Remember.',\n  description: 'Visual-first platform for civic reporting and community transparency',\n  version: '1.0.0',\n  author: 'MessageYou Team'\n};\n\n// API Configuration (Simulated)\nexport const API_CONFIG = {\n  baseUrl: '/api',\n  timeout: 5000,\n  retries: 3\n};\n\n// Entity Types\nexport const ENTITY_TYPES = {\n  PERSON: 'person',\n  BUSINESS: 'business',\n  PLACE: 'place',\n  VEHICLE: 'vehicle',\n  ITEM: 'item',\n  EVENT: 'event'\n};\n\n// Post Tags\nexport const POST_TAGS = {\n  REVIEW: 'review',\n  SCAM: 'scam',\n  LOST: 'lost',\n  FOUND: 'found',\n  GOOD_DEED: 'good_deed',\n  WARNING: 'warning',\n  QUESTION: 'question',\n  UPDATE: 'update'\n};\n\n// Reputation Levels\nexport const REPUTATION_LEVELS = {\n  EXCELLENT: { min: 4.5, label: 'Excellent', color: 'green' },\n  GOOD: { min: 3.5, label: 'Good', color: 'blue' },\n  AVERAGE: { min: 2.5, label: 'Average', color: 'yellow' },\n  POOR: { min: 1.5, label: 'Poor', color: 'orange' },\n  TERRIBLE: { min: 0, label: 'Terrible', color: 'red' }\n};\n\n// Animation Durations\nexport const ANIMATION_DURATION = {\n  FAST: 0.3,\n  NORMAL: 0.5,\n  SLOW: 0.8,\n  VERY_SLOW: 1.2\n};\n\n// Breakpoints (matching Tailwind)\nexport const BREAKPOINTS = {\n  SM: 640,\n  MD: 768,\n  LG: 1024,\n  XL: 1280,\n  '2XL': 1536\n};\n\n// Color Palette (Futuristic AI Theme)\nexport const COLORS = {\n  primary: {\n    50: '#f0f9ff',\n    100: '#e0f2fe',\n    200: '#bae6fd',\n    300: '#7dd3fc',\n    400: '#38bdf8',\n    500: '#0ea5e9',\n    600: '#0284c7',\n    700: '#0369a1',\n    800: '#075985',\n    900: '#0c4a6e'\n  },\n  accent: {\n    50: '#fdf4ff',\n    100: '#fae8ff',\n    200: '#f5d0fe',\n    300: '#f0abfc',\n    400: '#e879f9',\n    500: '#d946ef',\n    600: '#c026d3',\n    700: '#a21caf',\n    800: '#86198f',\n    900: '#701a75'\n  },\n  success: {\n    50: '#f0fdf4',\n    100: '#dcfce7',\n    200: '#bbf7d0',\n    300: '#86efac',\n    400: '#4ade80',\n    500: '#22c55e',\n    600: '#16a34a',\n    700: '#15803d',\n    800: '#166534',\n    900: '#14532d'\n  },\n  warning: {\n    50: '#fffbeb',\n    100: '#fef3c7',\n    200: '#fde68a',\n    300: '#fcd34d',\n    400: '#fbbf24',\n    500: '#f59e0b',\n    600: '#d97706',\n    700: '#b45309',\n    800: '#92400e',\n    900: '#78350f'\n  },\n  danger: {\n    50: '#fef2f2',\n    100: '#fee2e2',\n    200: '#fecaca',\n    300: '#fca5a5',\n    400: '#f87171',\n    500: '#ef4444',\n    600: '#dc2626',\n    700: '#b91c1c',\n    800: '#991b1b',\n    900: '#7f1d1d'\n  }\n};\n\n// Demo Data Configuration\nexport const DEMO_CONFIG = {\n  maxEntities: 50,\n  maxPostsPerEntity: 20,\n  maxCommentsPerPost: 10,\n  simulationDelay: 2000,\n  typingSpeed: 50\n};\n\n// Feature Flags\nexport const FEATURES = {\n  PHOTO_UPLOAD: true,\n  AI_ANALYSIS: true,\n  REAL_TIME_UPDATES: true,\n  NOTIFICATIONS: true,\n  DARK_MODE: true,\n  ANALYTICS: false,\n  BETA_FEATURES: true\n};\n\n// Navigation Menu Items\nexport const NAVIGATION = [\n  { name: 'Home', href: '/', icon: 'Home' },\n  { name: 'Demo', href: '/demo', icon: 'Play' },\n  { name: 'Pitch', href: '/pitch', icon: 'Presentation' },\n  { name: 'Why Us', href: '/why-us', icon: 'Users' },\n  { name: 'Roadmap', href: '/roadmap', icon: 'Map' },\n  { name: 'Sign Up', href: '/signup', icon: 'UserPlus' }\n];\n\n// Social Media Links\nexport const SOCIAL_LINKS = [\n  { name: 'Twitter', href: '#', icon: 'Twitter' },\n  { name: 'LinkedIn', href: '#', icon: 'Linkedin' },\n  { name: 'GitHub', href: '#', icon: 'Github' },\n  { name: 'Discord', href: '#', icon: 'MessageCircle' }\n];\n\n// Pricing Plans\nexport const PRICING_PLANS = [\n  {\n    name: 'Free',\n    price: 0,\n    period: 'forever',\n    description: 'Perfect for getting started',\n    features: [\n      'Basic photo upload',\n      'View public posts',\n      'Basic search',\n      'Limited posts (10/month)',\n      'Community support'\n    ],\n    cta: 'Get Started',\n    popular: false\n  },\n  {\n    name: 'Premium',\n    price: 4.99,\n    period: 'month',\n    description: 'For active community members',\n    features: [\n      'Unlimited posts',\n      'Advanced AI analysis',\n      'Priority search results',\n      'Trend alerts',\n      'Enhanced privacy',\n      'Email support'\n    ],\n    cta: 'Start Free Trial',\n    popular: true\n  },\n  {\n    name: 'Business',\n    price: 19.99,\n    period: 'month',\n    description: 'For businesses and organizations',\n    features: [\n      'Entity claiming',\n      'Business dashboard',\n      'Response tools',\n      'Analytics & insights',\n      'API access',\n      'Priority support'\n    ],\n    cta: 'Contact Sales',\n    popular: false\n  }\n];\n\n// Testimonials\nexport const TESTIMONIALS = [\n  {\n    name: 'Sarah Chen',\n    role: 'Community Organizer',\n    avatar: '/images/avatars/sarah.jpg',\n    content: 'MessageYou helped our neighborhood identify and stop a series of scams. The visual-first approach makes reporting so much easier.',\n    rating: 5\n  },\n  {\n    name: 'Mike Rodriguez',\n    role: 'Small Business Owner',\n    avatar: '/images/avatars/mike.jpg',\n    content: 'Being able to respond to community feedback directly has improved our customer relationships tremendously.',\n    rating: 5\n  },\n  {\n    name: 'Dr. Emily Watson',\n    role: 'Urban Planning Researcher',\n    avatar: '/images/avatars/emily.jpg',\n    content: 'This platform provides invaluable data for understanding community dynamics and safety patterns.',\n    rating: 5\n  }\n];\n\n// FAQ Data\nexport const FAQ_DATA = [\n  {\n    question: 'How does the photo recognition work?',\n    answer: 'Our AI analyzes uploaded photos to identify people, places, businesses, and objects, then matches them to existing entities in our database or creates new ones.'\n  },\n  {\n    question: 'Is my personal information safe?',\n    answer: 'We prioritize privacy with optional anonymous posting, data encryption, and strict compliance with privacy regulations like GDPR and CCPA.'\n  },\n  {\n    question: 'Can businesses respond to posts about them?',\n    answer: 'Yes, verified businesses can claim their entities and respond to posts, providing transparency and accountability for all parties.'\n  },\n  {\n    question: 'How do you prevent false reports?',\n    answer: 'We use AI-powered content moderation, community voting, and human review to identify and filter out false or malicious content.'\n  },\n  {\n    question: 'What types of entities can be reported?',\n    answer: 'You can report on people, businesses, places, vehicles, lost items, events, and more. If it exists in the real world, it can be documented on MessageYou.'\n  }\n];\n\n// Animation Presets\nexport const ANIMATION_PRESETS = {\n  fadeIn: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n    transition: { duration: 0.5 }\n  },\n  slideUp: {\n    initial: { opacity: 0, y: 50 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  },\n  slideDown: {\n    initial: { opacity: 0, y: -50 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.6 }\n  },\n  scaleIn: {\n    initial: { opacity: 0, scale: 0.8 },\n    animate: { opacity: 1, scale: 1 },\n    transition: { duration: 0.5 }\n  },\n  rotateIn: {\n    initial: { opacity: 0, rotate: -10 },\n    animate: { opacity: 1, rotate: 0 },\n    transition: { duration: 0.7 }\n  }\n};\n"], "names": [], "mappings": "AAAA,oBAAoB;;;;;;;;;;;;;;;;;;;AACb,MAAM,aAAa;IACxB,MAAM;IACN,SAAS;IACT,aAAa;IACb,SAAS;IACT,QAAQ;AACV;AAGO,MAAM,aAAa;IACxB,SAAS;IACT,SAAS;IACT,SAAS;AACX;AAGO,MAAM,eAAe;IAC1B,QAAQ;IACR,UAAU;IACV,OAAO;IACP,SAAS;IACT,MAAM;IACN,OAAO;AACT;AAGO,MAAM,YAAY;IACvB,QAAQ;IACR,MAAM;IACN,MAAM;IACN,OAAO;IACP,WAAW;IACX,SAAS;IACT,UAAU;IACV,QAAQ;AACV;AAGO,MAAM,oBAAoB;IAC/B,WAAW;QAAE,KAAK;QAAK,OAAO;QAAa,OAAO;IAAQ;IAC1D,MAAM;QAAE,KAAK;QAAK,OAAO;QAAQ,OAAO;IAAO;IAC/C,SAAS;QAAE,KAAK;QAAK,OAAO;QAAW,OAAO;IAAS;IACvD,MAAM;QAAE,KAAK;QAAK,OAAO;QAAQ,OAAO;IAAS;IACjD,UAAU;QAAE,KAAK;QAAG,OAAO;QAAY,OAAO;IAAM;AACtD;AAGO,MAAM,qBAAqB;IAChC,MAAM;IACN,QAAQ;IACR,MAAM;IACN,WAAW;AACb;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,SAAS;IACpB,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,SAAS;QACP,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;IACA,QAAQ;QACN,IAAI;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;IACP;AACF;AAGO,MAAM,cAAc;IACzB,aAAa;IACb,mBAAmB;IACnB,oBAAoB;IACpB,iBAAiB;IACjB,aAAa;AACf;AAGO,MAAM,WAAW;IACtB,cAAc;IACd,aAAa;IACb,mBAAmB;IACnB,eAAe;IACf,WAAW;IACX,WAAW;IACX,eAAe;AACjB;AAGO,MAAM,aAAa;IACxB;QAAE,MAAM;QAAQ,MAAM;QAAK,MAAM;IAAO;IACxC;QAAE,MAAM;QAAQ,MAAM;QAAS,MAAM;IAAO;IAC5C;QAAE,MAAM;QAAS,MAAM;QAAU,MAAM;IAAe;IACtD;QAAE,MAAM;QAAU,MAAM;QAAW,MAAM;IAAQ;IACjD;QAAE,MAAM;QAAW,MAAM;QAAY,MAAM;IAAM;IACjD;QAAE,MAAM;QAAW,MAAM;QAAW,MAAM;IAAW;CACtD;AAGM,MAAM,eAAe;IAC1B;QAAE,MAAM;QAAW,MAAM;QAAK,MAAM;IAAU;IAC9C;QAAE,MAAM;QAAY,MAAM;QAAK,MAAM;IAAW;IAChD;QAAE,MAAM;QAAU,MAAM;QAAK,MAAM;IAAS;IAC5C;QAAE,MAAM;QAAW,MAAM;QAAK,MAAM;IAAgB;CACrD;AAGM,MAAM,gBAAgB;IAC3B;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;IACA;QACE,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;IACX;CACD;AAGM,MAAM,eAAe;IAC1B;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;IACA;QACE,MAAM;QACN,MAAM;QACN,QAAQ;QACR,SAAS;QACT,QAAQ;IACV;CACD;AAGM,MAAM,WAAW;IACtB;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;IACA;QACE,UAAU;QACV,QAAQ;IACV;CACD;AAGM,MAAM,oBAAoB;IAC/B,QAAQ;QACN,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;QACtB,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,WAAW;QACT,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,UAAU;QACR,SAAS;YAAE,SAAS;YAAG,QAAQ,CAAC;QAAG;QACnC,SAAS;YAAE,SAAS;YAAG,QAAQ;QAAE;QACjC,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/src/app/layout.js"], "sourcesContent": ["import { Inter, JetBrains_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { APP_CONFIG } from \"@/lib/constants\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-sans\",\n});\n\nconst jetbrainsMono = JetBrains_Mono({\n  subsets: [\"latin\"],\n  variable: \"--font-mono\",\n});\n\nexport const metadata = {\n  title: `${APP_CONFIG.name} - ${APP_CONFIG.tagline}`,\n  description: APP_CONFIG.description,\n  keywords: [\"civic reporting\", \"community transparency\", \"AI\", \"photo recognition\", \"social platform\"],\n  authors: [{ name: APP_CONFIG.author }],\n  creator: APP_CONFIG.author,\n  publisher: APP_CONFIG.author,\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  icons: {\n    icon: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    shortcut: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n    apple: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n  },\n  manifest: \"/manifest.json\",\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: \"https://messageyou.ai\",\n    title: `${APP_CONFIG.name} - ${APP_CONFIG.tagline}`,\n    description: APP_CONFIG.description,\n    siteName: APP_CONFIG.name,\n    images: [\n      {\n        url: \"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\",\n        width: 1200,\n        height: 630,\n        alt: `${APP_CONFIG.name} Logo`,\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: `${APP_CONFIG.name} - ${APP_CONFIG.tagline}`,\n    description: APP_CONFIG.description,\n    images: [\"https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp\"],\n    creator: \"@messageyou\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n    yandex: \"your-yandex-verification-code\",\n  },\n};\n\nexport const viewport = {\n  width: \"device-width\",\n  initialScale: 1,\n  maximumScale: 1,\n  userScalable: false,\n  themeColor: [\n    { media: \"(prefers-color-scheme: light)\", color: \"#0ea5e9\" },\n    { media: \"(prefers-color-scheme: dark)\", color: \"#0ea5e9\" },\n  ],\n};\n\nexport default function RootLayout({ children }) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <head>\n        <link rel=\"preconnect\" href=\"https://fonts.googleapis.com\" />\n        <link rel=\"preconnect\" href=\"https://fonts.gstatic.com\" crossOrigin=\"anonymous\" />\n      </head>\n      <body\n        className={`${inter.variable} ${jetbrainsMono.variable} font-sans antialiased min-h-screen bg-background text-foreground`}\n        suppressHydrationWarning\n      >\n        <div className=\"relative flex min-h-screen flex-col\">\n          <div className=\"flex-1\">\n            {children}\n          </div>\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;;;;;;AAYO,MAAM,WAAW;IACtB,OAAO,GAAG,uHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,GAAG,EAAE,uHAAA,CAAA,aAAU,CAAC,OAAO,EAAE;IACnD,aAAa,uHAAA,CAAA,aAAU,CAAC,WAAW;IACnC,UAAU;QAAC;QAAmB;QAA0B;QAAM;QAAqB;KAAkB;IACrG,SAAS;QAAC;YAAE,MAAM,uHAAA,CAAA,aAAU,CAAC,MAAM;QAAC;KAAE;IACtC,SAAS,uHAAA,CAAA,aAAU,CAAC,MAAM;IAC1B,WAAW,uHAAA,CAAA,aAAU,CAAC,MAAM;IAC5B,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;IACA,UAAU;IACV,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK;QACL,OAAO,GAAG,uHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,GAAG,EAAE,uHAAA,CAAA,aAAU,CAAC,OAAO,EAAE;QACnD,aAAa,uHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,UAAU,uHAAA,CAAA,aAAU,CAAC,IAAI;QACzB,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK,GAAG,uHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,KAAK,CAAC;YAChC;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO,GAAG,uHAAA,CAAA,aAAU,CAAC,IAAI,CAAC,GAAG,EAAE,uHAAA,CAAA,aAAU,CAAC,OAAO,EAAE;QACnD,aAAa,uHAAA,CAAA,aAAU,CAAC,WAAW;QACnC,QAAQ;YAAC;SAA4F;QACrG,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;QACR,QAAQ;IACV;AACF;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,cAAc;IACd,cAAc;IACd,cAAc;IACd,YAAY;QACV;YAAE,OAAO;YAAiC,OAAO;QAAU;QAC3D;YAAE,OAAO;YAAgC,OAAO;QAAU;KAC3D;AACH;AAEe,SAAS,WAAW,EAAE,QAAQ,EAAE;IAC7C,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;;0BACtC,8OAAC;;kCACC,8OAAC;wBAAK,KAAI;wBAAa,MAAK;;;;;;kCAC5B,8OAAC;wBAAK,KAAI;wBAAa,MAAK;wBAA4B,aAAY;;;;;;;;;;;;0BAEtE,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAa,CAAC,QAAQ,CAAC,iEAAiE,CAAC;gBACzH,wBAAwB;0BAExB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 619, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i4-d2-messageyouai/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}